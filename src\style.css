@tailwind base;
@tailwind components;
@tailwind utilities;

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Glassmorphism Variables */
:root {
  /* Light mode glassmorphism */
  --glass-bg-light: rgba(255, 255, 255, 0.2);
  --glass-border-light: rgba(255, 255, 255, 0.3);
  --glass-text-light: rgba(0, 0, 26, 0.9);
  --glass-hover-light: rgba(255, 255, 255, 0.3);

  /* Dark mode glassmorphism */
  --glass-bg-dark: rgba(255, 255, 255, 0.1);
  --glass-border-dark: rgba(255, 255, 255, 0.2);
  --glass-text-dark: rgba(255, 255, 255, 0.9);
  --glass-hover-dark: rgba(255, 255, 255, 0.15);
}

/* Dynamic glassmorphism classes */
.glass-light {
  --glass-bg: var(--glass-bg-light);
  --glass-border: var(--glass-border-light);
  --glass-text: var(--glass-text-light);
  --glass-hover: var(--glass-hover-light);
}

.glass-dark {
  --glass-bg: var(--glass-bg-dark);
  --glass-border: var(--glass-border-dark);
  --glass-text: var(--glass-text-dark);
  --glass-hover: var(--glass-hover-dark);
}

/* Glassmorphism utility classes */
.glass-button {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: var(--glass-text);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  transition: all 0.2s ease-in-out;
}

.glass-button:hover {
  background: var(--glass-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Chat Animations */
@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(100px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-10px);
  }
  70% {
    transform: scale(0.95) translateY(5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slide-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

.animate-bounce-in {
  animation: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-slide-in {
  animation: slide-in 0.4s ease-out forwards;
  opacity: 0;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* Notification Animations */
@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out forwards;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out forwards;
}

.glass-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Custom scrollbar for glassmorphism */
.glass-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.glass-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.glass-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.glass-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: white;
  color: #00001a;
  transition: background-color 0.5s ease, color 0.5s ease;
}

body.dark {
  background-color: #00001a;
  color: white;
}

#app {
  min-height: 100vh;
}

/* Dynamic Glow Animations */
@keyframes pulse-glow-dark {
  0% {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.1), 0 4px 15px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.2), 0 0 30px rgba(59, 130, 246, 0.1), 0 6px 20px rgba(0, 0, 0, 0.15);
  }
  100% {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.1), 0 4px 15px rgba(0, 0, 0, 0.1);
  }
}

@keyframes pulse-glow-light {
  0% {
    box-shadow: 0 0 8px rgba(21, 58, 168, 0.2), 0 4px 12px rgba(0, 0, 0, 0.08);
  }
  50% {
    box-shadow: 0 0 16px rgba(21, 58, 168, 0.3), 0 0 24px rgba(21, 58, 168, 0.15), 0 6px 18px rgba(0, 0, 0, 0.12);
  }
  100% {
    box-shadow: 0 0 8px rgba(21, 58, 168, 0.2), 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

@keyframes glow-breathe {
  0% {
    box-shadow: 0 0 15px rgba(41, 46, 73, 0.15), 0 4px 15px rgba(41, 46, 73, 0.1);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 25px rgba(41, 46, 73, 0.25), 0 0 50px rgba(41, 46, 73, 0.1), 0 8px 25px rgba(41, 46, 73, 0.2);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 0 15px rgba(41, 46, 73, 0.15), 0 4px 15px rgba(41, 46, 73, 0.1);
    transform: scale(1);
  }
}

/* Hover enhancement animation */
.dynamic-glow-hover {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.dynamic-glow-hover:hover {
  animation: glow-breathe 2s ease-in-out infinite;
}

/* Global text color overrides for light mode */
body:not(.dark) .text-gray-600 {
  color: rgba(0, 0, 26, 0.7) !important;
}

/* Notification animations */
@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out;
}

/* Bounce in animation */
@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out;
}

/* Shrink animation for progress bars */
@keyframes shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

body:not(.dark) .text-gray-700 {
  color: rgba(0, 0, 26, 0.8) !important;
}

body:not(.dark) .text-gray-800 {
  color: rgba(0, 0, 26, 0.9) !important;
}

body:not(.dark) .text-gray-900 {
  color: rgba(0, 0, 26, 1) !important;
}

body:not(.dark) .text-gray-500 {
  color: rgba(0, 0, 26, 0.6) !important;
}

body:not(.dark) .text-gray-400 {
  color: rgba(0, 0, 26, 0.5) !important;
}

/* Remove any potential ">" symbols from pseudo-elements */
button::before,
button::after,
.flex::before,
.flex::after,
span::before,
span::after,
div::before,
div::after {
  content: none !important;
}

/* Specifically target any elements that might have ">" content */
*::before {
  content: none !important;
}

*::after {
  content: none !important;
}
